[project]
name = "retrieval-graph"
version = "0.0.1"
description = "Starter template for making a custom retrieval graph in LangGraph."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
license = { text = "MIT" }
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "langgraph>=0.2.6",
    "langchain-openai>=0.1.22",
    "langchain-anthropic>=0.1.23",
    "langchain>=0.2.14",
    "langchain-fireworks>=0.1.7",
    "python-dotenv>=1.0.1",
    "langchain-elasticsearch>=0.2.2,<0.3.0",
    "langchain-pinecone>=0.1.3,<0.2.0",
    "msgspec>=0.18.6",
    "langchain-mongodb>=0.1.9",
    "langchain-cohere>=0.2.4",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["langgraph.templates.retrieval_graph", "retrieval_graph"]
[tool.setuptools.package-dir]
"langgraph.templates.retrieval_graph" = "src/retrieval_graph"
"retrieval_graph" = "src/retrieval_graph"


[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
[tool.ruff.lint.pydocstyle]
convention = "google"
